#!/usr/bin/env node

/**
 * TEST LIVE JARVIS - DÉMONSTRATION COMPLÈTE
 * Test en temps réel des nouvelles fonctions PERPLEXITY-MPC HYBRIDE
 * 
 * L'agent JARVIS va démontrer :
 * 1. Recherche dans sa mémoire thermique (86+ milliards de neurones)
 * 2. Recherche sur le bureau local (fichiers/documents)
 * 3. Recherche sur Internet (connaissances générales)
 * 4. Apprentissage automatique et sauvegarde
 */

const DeepSeekR1AgentIntegrated = require('./core/deepseek-r1-agent-integrated');

class JarvisLiveDemo {
    constructor() {
        this.agent = null;
        this.testResults = [];
    }

    async init() {
        console.log('🎬 DÉMONSTRATION LIVE JARVIS - DÉMARRAGE');
        console.log('==========================================');
        console.log('🎯 Test des nouvelles fonctions PERPLEXITY-MPC HYBRIDE');
        console.log('🧠 Mémoire thermique + 💻 Bureau + 🌐 Internet');
        console.log('');

        // Initialisation de l'agent
        console.log('🔄 Initialisation de JARVIS...');
        this.agent = new DeepSeekR1AgentIntegrated();
        await this.agent.initialize();
        console.log('✅ JARVIS initialisé et prêt !');
        console.log('');
    }

    async runLiveDemo() {
        console.log('🎬 DÉBUT DE LA DÉMONSTRATION LIVE');
        console.log('==================================');
        console.log('');

        // Tests progressifs pour montrer chaque fonctionnalité
        const testScenarios = [
            {
                category: '🧠 MÉMOIRE THERMIQUE',
                description: 'Test de la recherche dans la mémoire thermique',
                questions: [
                    'quel est ton QI ?',
                    'que fais-tu ?'
                ]
            },
            {
                category: '🌐 RECHERCHE INTERNET',
                description: 'Test de la recherche sur Internet (fallback)',
                questions: [
                    'quelle est la capitale de la France ?',
                    'qui est Napoléon ?',
                    'qu\'est-ce que JavaScript ?'
                ]
            },
            {
                category: '💬 CONVERSATION NATURELLE',
                description: 'Test de conversation naturelle',
                questions: [
                    'salut JARVIS',
                    'bonjour comment ça va ?',
                    'merci pour ton aide'
                ]
            },
            {
                category: '🔍 RECHERCHE COMPLEXE',
                description: 'Test de recherche multi-sources',
                questions: [
                    'parle-moi de l\'intelligence artificielle',
                    'comment fonctionne un ordinateur ?',
                    'qu\'est-ce que Python ?'
                ]
            },
            {
                category: '🧪 APPRENTISSAGE',
                description: 'Test de l\'apprentissage automatique',
                questions: [
                    'apprends que Jean-Luc aime la programmation',
                    'que sais-tu sur Jean-Luc maintenant ?'
                ]
            }
        ];

        for (const scenario of testScenarios) {
            await this.runScenario(scenario);
        }

        await this.showFinalResults();
    }

    async runScenario(scenario) {
        console.log(`\n📋 ${scenario.category}`);
        console.log(`📝 ${scenario.description}`);
        console.log('─'.repeat(60));

        for (let i = 0; i < scenario.questions.length; i++) {
            const question = scenario.questions[i];
            console.log(`\n❓ Question ${i + 1}/${scenario.questions.length}: "${question}"`);
            
            const startTime = Date.now();
            
            try {
                // L'AGENT JARVIS RÉPOND EN LIVE
                const response = await this.agent.processMessage(question);
                const responseTime = Date.now() - startTime;
                
                // Affichage de la réponse
                console.log(`⏱️  Temps de réponse: ${responseTime}ms`);
                console.log(`🤖 JARVIS répond: "${response.message}"`);
                
                // Affichage des détails techniques
                if (response.method) {
                    console.log(`🔧 Méthode utilisée: ${response.method}`);
                }
                if (response.memories_used !== undefined) {
                    console.log(`🧠 Souvenirs utilisés: ${response.memories_used}`);
                }
                
                // Enregistrement du résultat
                this.testResults.push({
                    question: question,
                    response: response.message,
                    method: response.method,
                    responseTime: responseTime,
                    category: scenario.category
                });
                
                // Pause pour la lisibilité
                await this.sleep(1000);
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.testResults.push({
                    question: question,
                    error: error.message,
                    category: scenario.category
                });
            }
        }
    }

    async showFinalResults() {
        console.log('\n\n🎯 RÉSULTATS FINAUX DE LA DÉMONSTRATION');
        console.log('=========================================');
        
        const successfulTests = this.testResults.filter(r => !r.error);
        const failedTests = this.testResults.filter(r => r.error);
        
        console.log(`✅ Tests réussis: ${successfulTests.length}`);
        console.log(`❌ Tests échoués: ${failedTests.length}`);
        console.log(`📊 Total: ${this.testResults.length}`);
        
        if (successfulTests.length > 0) {
            const avgResponseTime = successfulTests.reduce((sum, r) => sum + r.responseTime, 0) / successfulTests.length;
            console.log(`⚡ Temps de réponse moyen: ${Math.round(avgResponseTime)}ms`);
        }
        
        // Affichage des méthodes utilisées
        console.log('\n🔧 MÉTHODES UTILISÉES:');
        const methods = {};
        successfulTests.forEach(r => {
            if (r.method) {
                methods[r.method] = (methods[r.method] || 0) + 1;
            }
        });
        
        Object.entries(methods).forEach(([method, count]) => {
            console.log(`   ${method}: ${count} fois`);
        });
        
        // Affichage des réponses par catégorie
        console.log('\n📋 RÉPONSES PAR CATÉGORIE:');
        const categories = {};
        this.testResults.forEach(r => {
            if (!categories[r.category]) {
                categories[r.category] = [];
            }
            categories[r.category].push(r);
        });
        
        Object.entries(categories).forEach(([category, results]) => {
            console.log(`\n${category}:`);
            results.forEach(r => {
                if (r.error) {
                    console.log(`   ❌ "${r.question}" → Erreur: ${r.error}`);
                } else {
                    console.log(`   ✅ "${r.question}" → "${r.response.substring(0, 50)}..."`);
                }
            });
        });
        
        console.log('\n🎉 DÉMONSTRATION TERMINÉE !');
        console.log('L\'agent JARVIS a démontré ses nouvelles capacités PERPLEXITY-MPC HYBRIDE !');
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async cleanup() {
        if (this.agent) {
            await this.agent.shutdown();
            console.log('✅ Agent JARVIS arrêté proprement');
        }
    }
}

// LANCEMENT DE LA DÉMONSTRATION LIVE
async function main() {
    const demo = new JarvisLiveDemo();
    
    try {
        await demo.init();
        await demo.runLiveDemo();
    } catch (error) {
        console.error('❌ Erreur durant la démonstration:', error);
    } finally {
        await demo.cleanup();
    }
}

// Gestion des signaux pour arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt demandé...');
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Arrêt demandé...');
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = JarvisLiveDemo;
