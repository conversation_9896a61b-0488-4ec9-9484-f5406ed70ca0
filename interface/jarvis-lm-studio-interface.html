<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 JARVIS - Interface LM Studio</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar style LM Studio */
        .sidebar {
            width: 280px;
            background: #2d2d2d;
            border-right: 1px solid #404040;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .sidebar-header {
            padding: 16px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidebar-header h1 {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
        }

        .model-section {
            padding: 16px;
            border-bottom: 1px solid #404040;
        }

        .model-section h3 {
            font-size: 14px;
            color: #888;
            margin-bottom: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .model-card {
            background: #3a3a3a;
            border: 1px solid #555;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .model-card:hover {
            background: #404040;
            border-color: #666;
        }

        .model-card.active {
            background: #007AFF;
            border-color: #007AFF;
        }

        .model-name {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .model-details {
            font-size: 12px;
            color: #aaa;
        }

        .jarvis-stats {
            padding: 16px;
            background: #252525;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 12px;
        }

        .stat-label {
            color: #888;
        }

        .stat-value {
            color: #00ff88;
            font-weight: 600;
        }

        /* Main content area */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #1a1a1a;
        }

        .chat-header {
            background: #2d2d2d;
            padding: 12px 20px;
            border-bottom: 1px solid #404040;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-title {
            font-size: 16px;
            font-weight: 600;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #00ff88;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            background: #00ff88;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-container {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #1a1a1a;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            flex-direction: column;
        }

        .message.user {
            align-items: flex-end;
        }

        .message.assistant {
            align-items: flex-start;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 12px;
            color: #888;
        }

        .message-avatar {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
        }

        .message.user .message-avatar {
            background: #007AFF;
            color: white;
        }

        .message.assistant .message-avatar {
            background: #00ff88;
            color: #000;
        }

        .message-content {
            max-width: 80%;
            padding: 16px 20px;
            border-radius: 12px;
            font-size: 14px;
            line-height: 1.6;
            white-space: pre-wrap;
        }

        .message.user .message-content {
            background: #007AFF;
            color: white;
        }

        .message.assistant .message-content {
            background: #2d2d2d;
            color: #ffffff;
            border: 1px solid #404040;
        }

        .thinking-indicator {
            display: none;
            align-items: center;
            gap: 12px;
            color: #888;
            font-size: 14px;
            margin-bottom: 16px;
            padding: 16px 20px;
            background: #252525;
            border-radius: 12px;
            max-width: 80%;
        }

        .thinking-dots {
            display: flex;
            gap: 4px;
        }

        .thinking-dot {
            width: 6px;
            height: 6px;
            background: #888;
            border-radius: 50%;
            animation: thinking 1.4s infinite;
        }

        .thinking-dot:nth-child(2) {
            animation-delay: 0.2s;
        }

        .thinking-dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes thinking {
            0%, 60%, 100% { opacity: 0.3; }
            30% { opacity: 1; }
        }

        .input-container {
            background: #2d2d2d;
            border-top: 1px solid #404040;
            padding: 16px 20px;
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .input-wrapper {
            flex: 1;
            position: relative;
        }

        #messageInput {
            width: 100%;
            background: #1a1a1a;
            border: 1px solid #404040;
            border-radius: 8px;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 14px;
            resize: none;
            min-height: 44px;
            max-height: 120px;
            font-family: inherit;
        }

        #messageInput:focus {
            outline: none;
            border-color: #007AFF;
        }

        #messageInput::placeholder {
            color: #888;
        }

        .send-button {
            background: #007AFF;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background 0.2s;
            min-width: 80px;
        }

        .send-button:hover {
            background: #0056CC;
        }

        .send-button:disabled {
            background: #404040;
            cursor: not-allowed;
        }

        .welcome-message {
            text-align: center;
            color: #888;
            font-size: 16px;
            margin-top: 40px;
        }

        .welcome-message h2 {
            color: #ffffff;
            margin-bottom: 8px;
        }

        /* Scrollbar styling */
        .chat-container::-webkit-scrollbar {
            width: 6px;
        }

        .chat-container::-webkit-scrollbar-track {
            background: #1a1a1a;
        }

        .chat-container::-webkit-scrollbar-thumb {
            background: #404040;
            border-radius: 3px;
        }

        .chat-container::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <!-- Sidebar LM Studio Style -->
    <div class="sidebar">
        <div class="sidebar-header">
            <span>🧠</span>
            <h1>JARVIS Studio</h1>
        </div>

        <div class="model-section">
            <h3>Modèles Chargés</h3>
            <div class="model-card active" id="jarvisModel">
                <div class="model-name">🧠 JARVIS Brain System</div>
                <div class="model-details">DeepSeek R1 + Mémoire Thermique</div>
            </div>
        </div>

        <div class="jarvis-stats">
            <div class="stat-item">
                <span class="stat-label">QI:</span>
                <span class="stat-value" id="qiValue">341.9</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Neurones:</span>
                <span class="stat-value" id="neuronsValue">86B+</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Mémoire:</span>
                <span class="stat-value" id="memoryValue">Thermique</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Statut:</span>
                <span class="stat-value" id="statusValue">Actif</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Processus:</span>
                <span class="stat-value" id="processValue">17 Actifs</span>
            </div>
            <div class="stat-item">
                <span class="stat-label">Battements:</span>
                <span class="stat-value" id="heartbeatValue">72 BPM</span>
            </div>
        </div>
    </div>

    <!-- Main Chat Area -->
    <div class="main-content">
        <div class="chat-header">
            <div class="chat-title">💬 Chat avec JARVIS</div>
            <div class="status-indicator">
                <div class="status-dot"></div>
                <span>Connecté</span>
            </div>
        </div>

        <div class="chat-container" id="chatContainer">
            <div class="welcome-message">
                <h2>🧠 Bienvenue dans JARVIS Studio</h2>
                <p>Votre cerveau artificiel personnel est prêt</p>
                <p style="margin-top: 8px; font-size: 14px; color: #666;">
                    Interface style LM Studio avec intégration directe - Zéro latence
                </p>
            </div>
        </div>

        <div class="thinking-indicator" id="thinkingIndicator">
            <div class="message-avatar">
                <span>🧠</span>
            </div>
            <span>JARVIS réfléchit avec ses 86 milliards de neurones...</span>
            <div class="thinking-dots">
                <div class="thinking-dot"></div>
                <div class="thinking-dot"></div>
                <div class="thinking-dot"></div>
            </div>
        </div>

        <div class="input-container">
            <div class="input-wrapper">
                <textarea
                    id="messageInput"
                    placeholder="Parlez à JARVIS..."
                    rows="1"
                ></textarea>
            </div>
            <button class="send-button" id="sendButton">Envoyer</button>
        </div>
    </div>

    <script>
        // Variables globales
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const thinkingIndicator = document.getElementById('thinkingIndicator');

        // Statistiques JARVIS en temps réel
        const stats = {
            qi: document.getElementById('qiValue'),
            neurons: document.getElementById('neuronsValue'),
            memory: document.getElementById('memoryValue'),
            status: document.getElementById('statusValue'),
            process: document.getElementById('processValue'),
            heartbeat: document.getElementById('heartbeatValue')
        };

        // Communication IPC avec le processus principal
        const { ipcRenderer } = require('electron');
        let isJarvisReady = false;

        // Auto-resize textarea
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Send message on Enter (but allow Shift+Enter for new line)
        messageInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });

        sendButton.addEventListener('click', sendMessage);

        // Écoute des événements JARVIS du processus principal
        console.log('🎧 Configuration des écouteurs IPC...');

        ipcRenderer.on('jarvis-ready', (event, data) => {
            console.log('🎉 ÉVÉNEMENT jarvis-ready reçu !', data);
            isJarvisReady = true;

            // Mise à jour des statistiques
            updateStats();

            // Démarrage de la mise à jour en temps réel
            setInterval(updateStats, 1000);

            // Message de bienvenue de JARVIS
            addMessage('assistant', 'Bonjour Jean-Luc ! Je suis JARVIS, ton cerveau artificiel personnel. Mes 86 milliards de neurones sont prêts à t\'aider. Comment puis-je t\'assister aujourd\'hui ?');

            console.log('✅ Interface JARVIS complètement initialisée !');
        });

        ipcRenderer.on('jarvis-error', (event, data) => {
            console.error('❌ Erreur JARVIS:', data.error);
            addMessage('system', '❌ Erreur lors de l\'initialisation de JARVIS: ' + data.error);
        });

        // Initialisation de JARVIS (via IPC)
        async function initializeJarvis() {
            try {
                console.log('🧠 Attente de l\'initialisation de JARVIS...');
                addMessage('system', '🔄 JARVIS en cours d\'initialisation...');

                // Vérification du statut JARVIS
                console.log('📞 Appel jarvis-status...');
                const status = await ipcRenderer.invoke('jarvis-status');
                console.log('📋 Statut JARVIS reçu:', status);

                if (status.ready) {
                    isJarvisReady = true;
                    console.log('✅ JARVIS déjà prêt !');
                    updateStats();
                    setInterval(updateStats, 1000);
                    addMessage('assistant', 'Bonjour Jean-Luc ! Je suis JARVIS, ton cerveau artificiel personnel. Mes 86 milliards de neurones sont prêts à t\'aider. Comment puis-je t\'assister aujourd\'hui ?');
                } else {
                    console.log('⏳ JARVIS pas encore prêt, attente de l\'événement jarvis-ready...');
                    addMessage('system', '⏳ JARVIS en cours d\'initialisation, veuillez patienter...');
                }

            } catch (error) {
                console.error('❌ Erreur vérification JARVIS:', error);
                addMessage('system', '❌ Erreur lors de la vérification de JARVIS: ' + error.message);
            }
        }

        // Mise à jour des statistiques en temps réel
        async function updateStats() {
            if (!isJarvisReady) return;

            try {
                const currentStats = await ipcRenderer.invoke('jarvis-stats');

                if (!currentStats) return;

                // Mise à jour QI
                if (currentStats.qi > 0) {
                    stats.qi.textContent = currentStats.qi.toFixed(1);
                }

                // Mise à jour neurones
                if (currentStats.neurons > 0) {
                    const neurons = currentStats.neurons;
                    stats.neurons.textContent = (neurons / **********).toFixed(1) + 'B';
                }

                // Mise à jour statut
                stats.status.textContent = isJarvisReady ? 'Actif' : 'Initialisation';

                // Mise à jour processus autonomes
                if (currentStats.processes > 0) {
                    stats.process.textContent = currentStats.processes + ' Actifs';
                }

                // Mise à jour zones mémoire
                if (currentStats.memory_zones > 0) {
                    stats.memory.textContent = `${currentStats.memory_zones} Zones`;
                }

            } catch (error) {
                console.error('Erreur mise à jour stats:', error);
            }
        }

        // Envoi de message avec intégration directe JARVIS
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Vérification que JARVIS est prêt
            if (!isJarvisReady) {
                addMessage('system', '⚠️ JARVIS est en cours d\'initialisation, veuillez patienter...');
                return;
            }

            // Ajout du message utilisateur
            addMessage('user', message);
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Affichage de l'indicateur de réflexion
            showThinkingIndicator();

            try {
                // INTÉGRATION DIRECTE - Appel via IPC au processus principal
                console.log('🧠 Envoi à JARVIS via IPC:', message);

                const result = await ipcRenderer.invoke('jarvis-process-message', message);

                console.log(`✅ Réponse JARVIS en ${result.responseTime}ms`);

                // Masquage de l'indicateur et affichage de la réponse
                hideThinkingIndicator();
                addMessage('assistant', result.response, result.responseTime);

            } catch (error) {
                console.error('❌ Erreur JARVIS:', error);
                hideThinkingIndicator();
                addMessage('system', '❌ Erreur lors du traitement: ' + error.message);
            }
        }

        // Ajout de message dans le chat
        function addMessage(sender, content, responseTime = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const time = new Date().toLocaleTimeString('fr-FR', {
                hour: '2-digit',
                minute: '2-digit'
            });

            let avatar = '';
            let senderName = '';

            if (sender === 'user') {
                avatar = 'JL';
                senderName = 'Jean-Luc';
            } else if (sender === 'assistant') {
                avatar = '🧠';
                senderName = 'JARVIS';
            } else {
                avatar = '⚠️';
                senderName = 'Système';
            }

            let responseTimeText = '';
            if (responseTime !== null) {
                responseTimeText = ` • ${responseTime}ms`;
            }

            messageDiv.innerHTML = `
                <div class="message-header">
                    <div class="message-avatar">${avatar}</div>
                    <span>${senderName}</span>
                    <span>${time}${responseTimeText}</span>
                </div>
                <div class="message-content">${content}</div>
            `;

            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;

            // Suppression du message de bienvenue
            const welcomeMessage = chatContainer.querySelector('.welcome-message');
            if (welcomeMessage) {
                welcomeMessage.remove();
            }
        }

        // Affichage de l'indicateur de réflexion
        function showThinkingIndicator() {
            thinkingIndicator.style.display = 'flex';
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }

        // Masquage de l'indicateur de réflexion
        function hideThinkingIndicator() {
            thinkingIndicator.style.display = 'none';
        }

        // Animation du battement cardiaque
        function animateHeartbeat() {
            const heartbeat = stats.heartbeat;
            heartbeat.style.color = '#ff4444';
            setTimeout(() => {
                heartbeat.style.color = '#00ff88';
            }, 100);
        }

        // Démarrage de l'animation du battement cardiaque
        setInterval(animateHeartbeat, 833); // 72 BPM = 833ms

        // Focus sur l'input au chargement
        window.addEventListener('load', () => {
            messageInput.focus();
            initializeJarvis();
        });

        // Gestion des erreurs globales
        window.addEventListener('error', (event) => {
            console.error('Erreur globale:', event.error);
            addMessage('system', '❌ Erreur système: ' + event.error.message);
        });

        // Gestion de la fermeture de l'application
        window.addEventListener('beforeunload', async () => {
            if (isJarvisReady) {
                console.log('🛑 Fermeture de l\'interface JARVIS...');
                // L'arrêt de JARVIS est géré par le processus principal
            }
        });
    </script>
</body>
</html>
