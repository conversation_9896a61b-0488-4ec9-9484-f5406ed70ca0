/**
 * 🌉 PONT JARVIS - Intégration directe Agent <-> Interface
 * 
 * Module de pont pour connecter l'interface à l'agent JARVIS
 * AUCUNE SIMULATION - Connexion directe au cerveau artificiel
 * 
 * Jean-<PERSON> PASSAVE - 2025
 */

const path = require('path');

class JarvisBridge {
    constructor() {
        this.agent = null;
        this.isReady = false;
        this.startTime = Date.now();
        this.stats = {
            qi: 0,
            neurons: 0,
            memoryZones: 0,
            processes: 0,
            heartbeat: 72
        };
    }

    // Initialisation de l'agent JARVIS
    async initialize() {
        try {
            console.log('🌉 Initialisation du pont JARVIS...');
            
            // Chargement du VRAI agent DeepSeek R1 8B
            const agentPath = path.join(__dirname, '..', 'core', 'deepseek-r1-8b-real-agent.js');
            console.log('📂 Chargement VRAI agent R1 8B:', agentPath);

            // Vérification de l'existence du fichier
            const fs = require('fs');
            if (!fs.existsSync(agentPath)) {
                throw new Error(`Agent DeepSeek R1 8B non trouvé: ${agentPath}`);
            }

            const AgentModule = require(agentPath);
            
            // Création de l'instance
            this.agent = new AgentModule();
            console.log('✅ Instance agent créée');
            
            // Initialisation
            console.log('🔄 Initialisation de l\'agent...');
            await this.agent.initialize();
            
            this.isReady = true;
            console.log('✅ Agent JARVIS prêt !');
            
            // Mise à jour des statistiques initiales
            this.updateStats();
            
            return true;
            
        } catch (error) {
            console.error('❌ Erreur initialisation pont JARVIS:', error);
            throw error;
        }
    }

    // Traitement d'un message
    async processMessage(message) {
        if (!this.isReady || !this.agent) {
            throw new Error('Agent JARVIS non initialisé');
        }

        try {
            console.log('🧠 Traitement message:', message);
            
            const startTime = Date.now();
            
            // Appel direct à l'agent - AUCUNE SIMULATION
            let response;
            
            if (typeof this.agent.processMessage === 'function') {
                response = await this.agent.processMessage(message);
            } else if (typeof this.agent.chat === 'function') {
                response = await this.agent.chat(message);
            } else if (typeof this.agent.respond === 'function') {
                response = await this.agent.respond(message);
            } else {
                // Méthode de fallback - recherche dans les méthodes disponibles
                const methods = Object.getOwnPropertyNames(Object.getPrototypeOf(this.agent));
                const chatMethods = methods.filter(m => 
                    m.includes('chat') || 
                    m.includes('message') || 
                    m.includes('respond') ||
                    m.includes('process')
                );
                
                if (chatMethods.length > 0) {
                    console.log('🔍 Méthodes disponibles:', chatMethods);
                    response = await this.agent[chatMethods[0]](message);
                } else {
                    throw new Error('Aucune méthode de traitement trouvée dans l\'agent');
                }
            }
            
            const responseTime = Date.now() - startTime;
            console.log(`✅ Réponse générée en ${responseTime}ms`);
            
            // Mise à jour des statistiques
            this.updateStats();
            
            return {
                response: response || 'Réponse vide de JARVIS',
                responseTime: responseTime,
                timestamp: new Date().toISOString()
            };
            
        } catch (error) {
            console.error('❌ Erreur traitement message:', error);
            throw error;
        }
    }

    // Mise à jour des statistiques
    updateStats() {
        if (!this.agent || !this.agent.thermalMemoryData) {
            return this.stats;
        }

        try {
            const data = this.agent.thermalMemoryData;
            
            // QI
            if (data.neural_system && data.neural_system.qi_level) {
                this.stats.qi = data.neural_system.qi_level;
            }
            
            // Neurones
            if (data.neural_system && data.neural_system.total_neurons) {
                this.stats.neurons = data.neural_system.total_neurons;
            }
            
            // Zones mémoire
            if (data.thermal_zones) {
                this.stats.memoryZones = Object.keys(data.thermal_zones).length;
            }
            
            // Processus autonomes
            if (this.agent.autonomousProcesses) {
                this.stats.processes = Object.keys(this.agent.autonomousProcesses).length;
            }
            
        } catch (error) {
            console.error('Erreur mise à jour stats:', error);
        }
        
        return this.stats;
    }

    // Récupération des statistiques
    getStats() {
        return {
            ...this.stats,
            isReady: this.isReady,
            uptime: Date.now() - this.startTime
        };
    }

    // Arrêt propre
    async shutdown() {
        if (this.agent && typeof this.agent.shutdown === 'function') {
            try {
                console.log('🛑 Arrêt de l\'agent JARVIS...');
                await this.agent.shutdown();
                console.log('✅ Agent JARVIS arrêté proprement');
            } catch (error) {
                console.error('❌ Erreur arrêt agent:', error);
            }
        }
        
        this.isReady = false;
        this.agent = null;
    }

    // Vérification de l'état
    isAgentReady() {
        return this.isReady && this.agent !== null;
    }

    // Récupération des informations de l'agent
    getAgentInfo() {
        if (!this.agent) {
            return null;
        }

        return {
            type: 'JARVIS Brain System',
            version: this.agent.version || '1.0.0',
            model: 'DeepSeek R1 + Mémoire Thermique',
            capabilities: [
                'Mémoire thermique persistante',
                '86+ milliards de neurones',
                'Processus autonomes',
                'Personnalité Claude intégrée',
                'QI adaptatif',
                'Apprentissage continu'
            ]
        };
    }
}

// Export du pont
module.exports = JarvisBridge;
