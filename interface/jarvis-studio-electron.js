#!/usr/bin/env node

/**
 * 🧠 JARVIS STUDIO - Interface LM Studio avec Agent Intégré
 * 
 * Interface identique à LM Studio mais avec JARVIS intégré directement
 * AUCUNE SIMULATION - Connexion directe au cerveau artificiel
 * 
 * Jean-<PERSON> PASSAVE - 2025
 */

const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// Configuration de l'application
const APP_CONFIG = {
    name: 'JARVIS Studio',
    version: '1.0.0',
    width: 1400,
    height: 900,
    minWidth: 1000,
    minHeight: 600
};

let mainWindow;
let jarvisAgent;
let jarvisBridge;

// Création de la fenêtre principale
function createMainWindow() {
    console.log('🚀 Création de la fenêtre JARVIS Studio...');
    
    mainWindow = new BrowserWindow({
        width: APP_CONFIG.width,
        height: APP_CONFIG.height,
        minWidth: APP_CONFIG.minWidth,
        minHeight: APP_CONFIG.minHeight,
        title: APP_CONFIG.name,
        icon: path.join(__dirname, 'assets', 'jarvis-icon.png'), // Optionnel
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false // Nécessaire pour charger les modules locaux
        },
        titleBarStyle: 'hiddenInset', // Style macOS
        backgroundColor: '#1a1a1a',
        show: false // Ne pas afficher avant que tout soit prêt
    });

    // Chargement de l'interface
    const interfacePath = path.join(__dirname, 'jarvis-lm-studio-interface.html');
    mainWindow.loadFile(interfacePath);

    // Affichage quand prêt
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Interface JARVIS Studio prête');
        mainWindow.show();
        
        // Focus sur la fenêtre
        if (process.platform === 'darwin') {
            app.dock.bounce();
        }
    });

    // Gestion de la fermeture
    mainWindow.on('closed', () => {
        console.log('🛑 Fermeture de JARVIS Studio');
        mainWindow = null;
        
        // Arrêt propre de JARVIS
        if (jarvisBridge && typeof jarvisBridge.shutdown === 'function') {
            jarvisBridge.shutdown().catch(console.error);
        }
    });

    // Gestion des erreurs de chargement
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
        console.error('❌ Erreur chargement interface:', errorCode, errorDescription);
    });

    // Console de développement (optionnel)
    if (process.env.NODE_ENV === 'development') {
        mainWindow.webContents.openDevTools();
    }
}

// Initialisation de JARVIS
async function initializeJarvis() {
    try {
        console.log('🧠 Initialisation de JARVIS...');

        const JarvisBridge = require('./jarvis-bridge.js');
        jarvisBridge = new JarvisBridge();

        await jarvisBridge.initialize();

        console.log('✅ JARVIS initialisé avec succès !');

        // Notifier l'interface que JARVIS est prêt
        if (mainWindow && !mainWindow.isDestroyed()) {
            console.log('📤 Envoi notification jarvis-ready à l\'interface...');
            mainWindow.webContents.send('jarvis-ready', {
                ready: true,
                stats: jarvisBridge.getStats()
            });
            console.log('✅ Notification jarvis-ready envoyée');
        } else {
            console.log('⚠️ Fenêtre principale non disponible pour notification');
        }

        return true;

    } catch (error) {
        console.error('❌ Erreur initialisation JARVIS:', error);

        // Notifier l'interface de l'erreur
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.webContents.send('jarvis-error', {
                error: error.message
            });
        }

        return false;
    }
}

// Initialisation de l'application
app.whenReady().then(async () => {
    console.log('🧠 Démarrage de JARVIS Studio...');
    console.log(`📍 Répertoire: ${__dirname}`);

    createMainWindow();

    // Initialisation de JARVIS après la création de la fenêtre
    setTimeout(() => {
        initializeJarvis();
    }, 1000);

    // Gestion macOS - Réouverture de la fenêtre
    app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
            createMainWindow();
        }
    });
});

// Fermeture de l'application
app.on('window-all-closed', () => {
    console.log('🛑 Fermeture de toutes les fenêtres');
    
    // Sur macOS, garder l'app active même sans fenêtre
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Gestion de la fermeture propre
app.on('before-quit', async (event) => {
    console.log('🛑 Arrêt de l\'application...');
    
    if (jarvisBridge && typeof jarvisBridge.shutdown === 'function') {
        event.preventDefault();

        try {
            await jarvisBridge.shutdown();
            console.log('✅ JARVIS arrêté proprement');
        } catch (error) {
            console.error('❌ Erreur arrêt JARVIS:', error);
        } finally {
            app.exit(0);
        }
    }
});

// Communication avec le processus de rendu (interface)
ipcMain.handle('jarvis-status', () => {
    const isReady = jarvisBridge ? jarvisBridge.isAgentReady() : false;
    console.log(`📋 jarvis-status appelé: jarvisBridge=${!!jarvisBridge}, isReady=${isReady}`);

    if (jarvisBridge) {
        console.log(`📋 jarvisBridge.isReady=${jarvisBridge.isReady}, jarvisBridge.agent=${!!jarvisBridge.agent}`);
    }

    return {
        ready: isReady,
        version: APP_CONFIG.version,
        timestamp: new Date().toISOString()
    };
});

ipcMain.handle('jarvis-stats', () => {
    if (!jarvisBridge || !jarvisBridge.isAgentReady()) {
        return null;
    }

    try {
        return jarvisBridge.getStats();
    } catch (error) {
        console.error('Erreur récupération stats:', error);
        return null;
    }
});

// Handler pour traiter les messages
ipcMain.handle('jarvis-process-message', async (event, message) => {
    if (!jarvisBridge || !jarvisBridge.isAgentReady()) {
        throw new Error('JARVIS non prêt');
    }

    try {
        console.log('🧠 Traitement message via IPC:', message);
        const result = await jarvisBridge.processMessage(message);
        console.log(`✅ Réponse générée en ${result.responseTime}ms`);
        return result;
    } catch (error) {
        console.error('❌ Erreur traitement message:', error);
        throw error;
    }
});

// Handler pour récupérer les informations de l'agent
ipcMain.handle('jarvis-info', () => {
    if (!jarvisBridge || !jarvisBridge.isAgentReady()) {
        return null;
    }

    try {
        return jarvisBridge.getAgentInfo();
    } catch (error) {
        console.error('Erreur récupération info agent:', error);
        return null;
    }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
    
    if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('system-error', {
            message: error.message,
            stack: error.stack
        });
    }
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée:', reason);
});

// Export pour tests
module.exports = {
    createMainWindow,
    APP_CONFIG
};

console.log('🧠 JARVIS Studio - Prêt au démarrage');
console.log('💙 Interface LM Studio avec cerveau artificiel intégré');
console.log('🚀 Zéro latence - Réflexion instantanée');
