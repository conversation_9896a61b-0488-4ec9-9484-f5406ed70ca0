# Creating a release

Only collaborators in npm for **node-addon-api** can create releases.
If you want to be able to do releases ask one of the existing
collaborators to add you. If necessary you can ask the build
Working Group who manages the Node.js npm user to add you if
there are no other active collaborators.

## Prerequisites

Before to start creating a new release check if you have installed the following
tools:

* [Changelog maker](https://www.npmjs.com/package/changelog-maker)

If not please follow the instruction reported in the tool's documentation to 
install it.

## Publish new release

These are the steps to follow to create a new release:

* Open an issue in the **node-addon-api** repo documenting the intent to create a
new release. Give people some time to comment or suggest PRs that should land first.

* Validate all tests pass by running npm test on master.

* Update the version in **package.json** appropriately.

* Update the [README.md](https://github.com/nodejs/node-addon-api/blob/master/README.md) 
to show the new version as the latest.

* Generate the changelog for the new version using **changelog maker** tool. From
the route folder of the repo launch the following command:

    ```bash 
    > changelog-maker
    ```
* Use the output generated by **changelog maker** to pdate the [CHANGELOG.md](https://github.com/nodejs/node-addon-api/blob/master/CHANGELOG.md)
following the style used in publishing the previous release.

* Add any new contributors to the "contributors" section in the package.json

* Validate all tests pass by running npm test on master.

* Use **[CI](https://ci.nodejs.org/view/x%20-%20Abi%20stable%20module%20API/job/node-test-node-addon-api/)**
to validate tests pass for latest 11, 10, 8, 6 releases (note there are still some issues on SmartOS and
Windows in the testing).

* Do a clean checkout of node-addon-api.

* Login and then run `npm publish`.

* Create a release in Github (look at existing releases for an example).

* Validate that you can run `npm install node-addon-api` successfully
and that the correct version is installed.

* Comment on the issue opened in the first step that the release has been created
and close the issue.

* Tweet that the release has been created.
