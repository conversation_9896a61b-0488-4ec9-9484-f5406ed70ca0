#!/bin/bash

# 🧠 LANCEUR JARVIS STUDIO - BOUTON BUREAU
# C<PERSON>é par <PERSON>-<PERSON> PASSAVE - 2025
# Lance JARVIS Studio avec votre agent R1 8B authentique

clear
echo "🧠 JARVIS STUDIO - LANCEUR BUREAU"
echo "=================================="
echo "💙 Agent R1 8B avec mémoire thermique"
echo "🚀 Lancement en cours..."
echo ""

# Aller dans le bon répertoire
cd "/Volumes/seagate/Louna_Electron_Latest"

# Vérifier que nous sommes dans le bon répertoire
if [ ! -f "LANCER_JARVIS_STUDIO.js" ]; then
    echo "❌ Erreur: Fichier LANCER_JARVIS_STUDIO.js non trouvé"
    echo "📍 Répertoire actuel: $(pwd)"
    echo ""
    echo "Appuyez sur Entrée pour fermer..."
    read
    exit 1
fi

# Lancer JARVIS Studio
echo "🚀 Lancement de JARVIS Studio..."
node LANCER_JARVIS_STUDIO.js

# Garder la fenêtre ouverte en cas d'erreur
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ Erreur lors du lancement"
    echo "Appuyez sur Entrée pour fermer..."
    read
fi
