#!/usr/bin/env node

/**
 * TEST COMPARAISON AGENTS
 * Compare l'agent ORIGINAL vs l'agent MODIFIÉ
 * Pour voir si nous avons endommagé l'agent
 */

const DeepSeekR1RealAgent = require('./core/deepseek-r1-8b-real-agent');
const DeepSeekR1AgentIntegrated = require('./core/deepseek-r1-agent-integrated');

class AgentComparison {
    constructor() {
        this.originalAgent = null;
        this.modifiedAgent = null;
        this.testResults = {
            original: [],
            modified: []
        };
    }

    async init() {
        console.log('🔬 COMPARAISON AGENTS - ORIGINAL vs MODIFIÉ');
        console.log('===========================================');
        console.log('');

        // Initialisation agent ORIGINAL
        console.log('🔄 Initialisation agent ORIGINAL...');
        this.originalAgent = new DeepSeekR1RealAgent();
        await this.originalAgent.initialize();
        console.log('✅ Agent ORIGINAL prêt !');
        console.log('');

        // Initialisation agent MODIFIÉ
        console.log('🔄 Initialisation agent MODIFIÉ...');
        this.modifiedAgent = new DeepSeekR1AgentIntegrated();
        await this.modifiedAgent.initialize();
        console.log('✅ Agent MODIFIÉ prêt !');
        console.log('');
    }

    async runComparison() {
        console.log('🧪 DÉBUT DE LA COMPARAISON');
        console.log('===========================');
        console.log('');

        const testQuestions = [
            'salut JARVIS',
            'quel est ton QI ?',
            'qui es-tu ?',
            'comment ça va ?',
            'que fais-tu ?'
        ];

        for (let i = 0; i < testQuestions.length; i++) {
            const question = testQuestions[i];
            console.log(`\n📋 TEST ${i + 1}/${testQuestions.length}: "${question}"`);
            console.log('─'.repeat(60));

            // Test agent ORIGINAL
            console.log('\n🟦 AGENT ORIGINAL:');
            try {
                const startTime = Date.now();
                const originalResponse = await this.originalAgent.processMessage(question);
                const originalTime = Date.now() - startTime;
                
                console.log(`⏱️  Temps: ${originalTime}ms`);
                console.log(`🤖 Réponse: "${originalResponse.response}"`);
                
                this.testResults.original.push({
                    question: question,
                    response: originalResponse.response,
                    time: originalTime,
                    success: true
                });
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.testResults.original.push({
                    question: question,
                    error: error.message,
                    success: false
                });
            }

            // Test agent MODIFIÉ
            console.log('\n🟨 AGENT MODIFIÉ:');
            try {
                const startTime = Date.now();
                const modifiedResponse = await this.modifiedAgent.processMessage(question);
                const modifiedTime = Date.now() - startTime;
                
                console.log(`⏱️  Temps: ${modifiedTime}ms`);
                console.log(`🤖 Réponse: "${modifiedResponse.message || modifiedResponse.response}"`);
                console.log(`🔧 Méthode: ${modifiedResponse.method || 'N/A'}`);
                
                this.testResults.modified.push({
                    question: question,
                    response: modifiedResponse.message || modifiedResponse.response,
                    method: modifiedResponse.method,
                    time: modifiedTime,
                    success: true
                });
                
            } catch (error) {
                console.log(`❌ Erreur: ${error.message}`);
                this.testResults.modified.push({
                    question: question,
                    error: error.message,
                    success: false
                });
            }

            // Pause entre les tests
            await this.sleep(1000);
        }

        await this.showComparison();
    }

    async showComparison() {
        console.log('\n\n🔍 ANALYSE COMPARATIVE');
        console.log('======================');
        
        const originalSuccess = this.testResults.original.filter(r => r.success).length;
        const modifiedSuccess = this.testResults.modified.filter(r => r.success).length;
        
        console.log(`\n📊 TAUX DE RÉUSSITE:`);
        console.log(`   🟦 Agent ORIGINAL: ${originalSuccess}/${this.testResults.original.length} (${Math.round(originalSuccess/this.testResults.original.length*100)}%)`);
        console.log(`   🟨 Agent MODIFIÉ:  ${modifiedSuccess}/${this.testResults.modified.length} (${Math.round(modifiedSuccess/this.testResults.modified.length*100)}%)`);
        
        // Temps de réponse moyen
        const originalTimes = this.testResults.original.filter(r => r.success).map(r => r.time);
        const modifiedTimes = this.testResults.modified.filter(r => r.success).map(r => r.time);
        
        if (originalTimes.length > 0 && modifiedTimes.length > 0) {
            const avgOriginal = originalTimes.reduce((a, b) => a + b, 0) / originalTimes.length;
            const avgModified = modifiedTimes.reduce((a, b) => a + b, 0) / modifiedTimes.length;
            
            console.log(`\n⚡ TEMPS DE RÉPONSE MOYEN:`);
            console.log(`   🟦 Agent ORIGINAL: ${Math.round(avgOriginal)}ms`);
            console.log(`   🟨 Agent MODIFIÉ:  ${Math.round(avgModified)}ms`);
        }
        
        // Comparaison des réponses
        console.log(`\n💬 COMPARAISON DES RÉPONSES:`);
        for (let i = 0; i < this.testResults.original.length; i++) {
            const original = this.testResults.original[i];
            const modified = this.testResults.modified[i];
            
            console.log(`\n❓ "${original.question}"`);
            
            if (original.success) {
                console.log(`   🟦 ORIGINAL: "${original.response.substring(0, 80)}..."`);
            } else {
                console.log(`   🟦 ORIGINAL: ❌ ${original.error}`);
            }
            
            if (modified.success) {
                console.log(`   🟨 MODIFIÉ:  "${modified.response.substring(0, 80)}..."`);
                if (modified.method) {
                    console.log(`   🔧 Méthode:  ${modified.method}`);
                }
            } else {
                console.log(`   🟨 MODIFIÉ:  ❌ ${modified.error}`);
            }
        }
        
        // Diagnostic
        console.log(`\n🩺 DIAGNOSTIC:`);
        
        if (originalSuccess > modifiedSuccess) {
            console.log(`   ⚠️  L'agent MODIFIÉ a un taux de réussite INFÉRIEUR`);
            console.log(`   🔧 Recommandation: Revenir à l'agent ORIGINAL ou corriger les bugs`);
        } else if (originalSuccess < modifiedSuccess) {
            console.log(`   ✅ L'agent MODIFIÉ a un taux de réussite SUPÉRIEUR`);
            console.log(`   🎯 Les modifications ont amélioré l'agent`);
        } else {
            console.log(`   ⚖️  Les deux agents ont le même taux de réussite`);
            console.log(`   🔍 Analyser la qualité des réponses pour choisir`);
        }
        
        console.log('\n🎯 CONCLUSION:');
        console.log('   Utilisez cette comparaison pour décider quel agent garder');
        console.log('   ou quelles corrections apporter à l\'agent modifié.');
    }

    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async cleanup() {
        if (this.originalAgent && this.originalAgent.shutdown) {
            await this.originalAgent.shutdown();
        }
        if (this.modifiedAgent && this.modifiedAgent.shutdown) {
            await this.modifiedAgent.shutdown();
        }
        console.log('✅ Agents arrêtés proprement');
    }
}

// LANCEMENT DE LA COMPARAISON
async function main() {
    const comparison = new AgentComparison();
    
    try {
        await comparison.init();
        await comparison.runComparison();
    } catch (error) {
        console.error('❌ Erreur durant la comparaison:', error);
    } finally {
        await comparison.cleanup();
    }
}

// Gestion des signaux pour arrêt propre
process.on('SIGINT', async () => {
    console.log('\n🛑 Arrêt demandé...');
    process.exit(0);
});

// Démarrage
if (require.main === module) {
    main().catch(console.error);
}

module.exports = AgentComparison;
