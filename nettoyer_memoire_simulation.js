#!/usr/bin/env node

/**
 * NETTOYAGE MÉMOIRE THERMIQUE - SUPPRESSION DES SIMULATIONS
 * Supprime tous les souvenirs contenant des réponses simulées
 */

const fs = require('fs');
const path = require('path');

async function nettoyerMemoireSimulation() {
    console.log('🧹 NETTOYAGE MÉMOIRE THERMIQUE - SUPPRESSION SIMULATIONS');
    console.log('======================================================');
    
    try {
        const memoirePath = 'thermal_memory_persistent.json';
        
        // Lecture de la mémoire thermique
        console.log('📖 Lecture de la mémoire thermique...');
        const memoireData = JSON.parse(fs.readFileSync(memoirePath, 'utf8'));
        
        // Fragments simulés à supprimer (recherche plus large)
        const fragmentsSimules = [
            'Sal<PERSON>',
            'Bonjou<PERSON> <PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>, mon créateur',
            'Je vous reconnais immédiatement',
            '86 milliards de neurones sont à votre service',
            'assistant IA personnel',
            'cerveau artificiel personnel',
            'capacités de raisonnement avancées',
            'réflexion intégrée',
            'processus neuronaux authentiques',
            'Ma mémoire thermique me rappelle',
            'Actuellement, mes neurones sont actifs',
            'Comment puis-je vous aider aujourd\'hui',
            'En me basant sur ma mémoire thermique',
            'Laissez-moi vous expliquer',
            'votre assistant IA personnel',
            'Je suis JARVIS, votre',
            'mes processus neuronaux',
            'avec mes capacités',
            'Je traite votre',
            'Je vais traiter',
            'Je comprends votre message'
        ];
        
        let souvenirsSupprimés = 0;
        let totalSouvenirs = 0;
        
        // Nettoyage de chaque zone thermique
        for (const [zoneName, zone] of Object.entries(memoireData.thermal_zones || {})) {
            console.log(`🔍 Nettoyage zone: ${zoneName}`);
            
            if (zone.entries && Array.isArray(zone.entries)) {
                const memoiresOriginales = zone.entries.length;
                totalSouvenirs += memoiresOriginales;

                // Filtrage des souvenirs
                zone.entries = zone.entries.filter(memory => {
                    if (!memory.content) return true;
                    
                    // Vérifier si le souvenir contient un fragment simulé
                    for (const fragmentSimule of fragmentsSimules) {
                        if (memory.content.includes(fragmentSimule)) {
                            console.log(`❌ FRAGMENT SIMULÉ SUPPRIMÉ: "${memory.content.substring(0, 80)}..."`);
                            souvenirsSupprimés++;
                            return false;
                        }
                    }

                    // Vérifier les conversations avec réponses simulées
                    if (memory.content.startsWith('Conversation:')) {
                        for (const fragmentSimule of fragmentsSimules) {
                            if (memory.content.includes(fragmentSimule)) {
                                console.log(`❌ CONVERSATION SIMULÉE SUPPRIMÉE: "${memory.content.substring(0, 80)}..."`);
                                souvenirsSupprimés++;
                                return false;
                            }
                        }
                    }
                    
                    return true;
                });
                
                const memoiresRestantes = zone.entries.length;
                console.log(`   📊 ${memoiresOriginales} → ${memoiresRestantes} souvenirs (${memoiresOriginales - memoiresRestantes} supprimés)`);
            }
        }
        
        // Nettoyage des souvenirs racine (si ils existent)
        if (memoireData.memories && Array.isArray(memoireData.memories)) {
            console.log('🔍 Nettoyage souvenirs racine...');
            const memoiresOriginales = memoireData.memories.length;
            totalSouvenirs += memoiresOriginales;
            
            memoireData.memories = memoireData.memories.filter(memory => {
                if (!memory.content) return true;
                
                for (const fragmentSimule of fragmentsSimules) {
                    if (memory.content.includes(fragmentSimule)) {
                        console.log(`❌ FRAGMENT RACINE SUPPRIMÉ: "${memory.content.substring(0, 80)}..."`);
                        souvenirsSupprimés++;
                        return false;
                    }
                }
                
                return true;
            });
            
            const memoiresRestantes = memoireData.memories.length;
            console.log(`   📊 ${memoiresOriginales} → ${memoiresRestantes} souvenirs racine (${memoiresOriginales - memoiresRestantes} supprimés)`);
        }
        
        // Sauvegarde de la mémoire nettoyée
        console.log('💾 Sauvegarde de la mémoire nettoyée...');
        
        // Backup de l'ancienne mémoire
        const backupPath = `thermal_memory_backup_${Date.now()}.json`;
        fs.writeFileSync(backupPath, fs.readFileSync(memoirePath));
        console.log(`📦 Backup créé: ${backupPath}`);
        
        // Sauvegarde de la nouvelle mémoire
        fs.writeFileSync(memoirePath, JSON.stringify(memoireData, null, 2));
        
        console.log('\n📊 RÉSULTATS DU NETTOYAGE :');
        console.log('===========================');
        console.log(`📈 Total souvenirs analysés: ${totalSouvenirs}`);
        console.log(`🗑️  Souvenirs simulés supprimés: ${souvenirsSupprimés}`);
        console.log(`✅ Souvenirs authentiques conservés: ${totalSouvenirs - souvenirsSupprimés}`);
        console.log(`📉 Pourcentage de simulation supprimé: ${((souvenirsSupprimés / totalSouvenirs) * 100).toFixed(1)}%`);
        
        if (souvenirsSupprimés > 0) {
            console.log('\n🎉 NETTOYAGE TERMINÉ ! La mémoire thermique est maintenant 100% authentique !');
        } else {
            console.log('\n✅ Aucune simulation détectée - La mémoire était déjà authentique !');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ ERREUR NETTOYAGE:', error);
        return false;
    }
}

// Lancement du nettoyage
if (require.main === module) {
    nettoyerMemoireSimulation()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ ERREUR:', error);
            process.exit(1);
        });
}

module.exports = { nettoyerMemoireSimulation };
