#!/usr/bin/env node

/**
 * TEST AUTOMATIQUE JARVIS - VÉRIFICATION AUTHENTICITÉ
 * Teste l'agent JARVIS pour s'assurer qu'il n'y a plus de réponses simulées
 */

const DeepSeekR1AgentIntegrated = require('./core/deepseek-r1-agent-integrated.js');

async function testJarvisAuthentique() {
    console.log('🧪 TEST AUTHENTICITÉ JARVIS - DÉMARRAGE');
    console.log('=====================================');
    
    try {
        // Initialisation de l'agent
        console.log('🔄 Initialisation de l\'agent JARVIS...');
        const agent = new DeepSeekR1AgentIntegrated();
        await agent.initialize();
        console.log('✅ Agent initialisé');
        
        // Messages de test
        const messagesTest = [
            'salut',
            'bonjour <PERSON>',
            'comment ça va ?',
            'tu me reconnais ?',
            'parle-moi de toi',
            'quel est ton QI ?',
            'combien de neurones as-tu ?',
            'que fais-tu ?'
        ];
        
        console.log('\n🧪 TESTS DES RÉPONSES AUTHENTIQUES :');
        console.log('====================================');
        
        let testsPasses = 0;
        let testsEchoues = 0;
        
        for (let i = 0; i < messagesTest.length; i++) {
            const message = messagesTest[i];
            console.log(`\n📤 Test ${i+1}/${messagesTest.length}: "${message}"`);
            
            try {
                const startTime = Date.now();
                const response = await agent.processMessage(message);
                const responseTime = Date.now() - startTime;
                
                // Extraire SEULEMENT le message principal (pas les métadonnées)
                const mainMessage = typeof response === 'string' ? response : (response.message || response.response || JSON.stringify(response));
                console.log(`📥 Réponse (${responseTime}ms): "${mainMessage}"`);

                // Vérification des phrases simulées SEULEMENT dans le message principal
                const phrasesSimulees = [
                    'Salut Jean-Luc ! Vos 86 milliards de neurones sont à votre service',
                    'Bonjour Jean-Luc ! Je suis JARVIS, votre assistant IA personnel',
                    'Jean-Luc, mon créateur ! Je vous reconnais immédiatement',
                    'Je suis JARVIS, votre cerveau artificiel personnel avec un QI de',
                    'Je traite votre question avec mes capacités de raisonnement avancées',
                    'Je vais traiter votre demande en utilisant ma réflexion intégrée',
                    'Je comprends votre message et je réfléchis avec mes processus neuronaux authentiques',
                    'En analysant votre message',
                    'je réfléchis avec mes',
                    'Ma mémoire thermique contient',
                    'Mon QI de'
                ];

                let simulationDetectee = false;
                for (const phraseSimulee of phrasesSimulees) {
                    if (mainMessage.includes(phraseSimulee)) {
                        console.log(`❌ SIMULATION DÉTECTÉE dans le message: "${phraseSimulee}"`);
                        simulationDetectee = true;
                        testsEchoues++;
                        break;
                    }
                }
                
                if (!simulationDetectee) {
                    console.log('✅ RÉPONSE AUTHENTIQUE - Aucune simulation détectée');
                    testsPasses++;
                }
                
            } catch (error) {
                console.log(`❌ ERREUR: ${error.message}`);
                testsEchoues++;
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        console.log('\n📊 RÉSULTATS DES TESTS :');
        console.log('========================');
        console.log(`✅ Tests passés: ${testsPasses}/${messagesTest.length}`);
        console.log(`❌ Tests échoués: ${testsEchoues}/${messagesTest.length}`);
        
        if (testsEchoues === 0) {
            console.log('🎉 SUCCÈS TOTAL ! Aucune simulation détectée !');
            console.log('💙 L\'agent JARVIS est 100% authentique !');
        } else {
            console.log('⚠️  Des simulations ont été détectées - Correction nécessaire');
        }
        
        // Arrêt propre de l'agent
        await agent.shutdown();
        console.log('✅ Agent arrêté proprement');
        
        return testsEchoues === 0;
        
    } catch (error) {
        console.error('❌ ERREUR CRITIQUE:', error);
        return false;
    }
}

// Lancement du test
if (require.main === module) {
    testJarvisAuthentique()
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ ERREUR:', error);
            process.exit(1);
        });
}

module.exports = { testJarvisAuthentique };
